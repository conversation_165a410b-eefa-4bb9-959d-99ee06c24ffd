import { useState } from 'react'
import './App.css'
import ThemeSelector from './components/ThemeSelector'
import TextInputSection from './components/TextInputSection'
import FileUploadSection from './components/FileUploadSection'
import ConfigurationSection from './components/ConfigurationSection'
import ResultsSection from './components/ResultsSection'

function App() {
  const [currentTheme, setCurrentTheme] = useState('lumen')
  const [textInput, setTextInput] = useState('')
  const [uploadedFiles, setUploadedFiles] = useState([])
  const [activeInputTab, setActiveInputTab] = useState('text') // 'text' or 'files'
  const [configurations, setConfigurations] = useState({
    analysisType: 'sentiment',
    includeMetrics: true,
    outputFormat: 'json',
    language: 'en'
  })
  const [results, setResults] = useState(null)
  const [isProcessing, setIsProcessing] = useState(false)

  // Handle mutual exclusivity between text and file inputs
  const handleTextChange = (text) => {
    setTextInput(text)
    if (text.trim() && uploadedFiles.length > 0) {
      setUploadedFiles([]) // Clear files when text is entered
    }
    if (text.trim() && activeInputTab !== 'text') {
      setActiveInputTab('text')
    }
  }

  const handleFilesChange = (files) => {
    setUploadedFiles(files)
    if (files.length > 0 && textInput.trim()) {
      setTextInput('') // Clear text when files are uploaded
    }
    if (files.length > 0 && activeInputTab !== 'files') {
      setActiveInputTab('files')
    }
  }

  const handleTabSwitch = (tab) => {
    setActiveInputTab(tab)
    // Clear the other input when switching tabs
    if (tab === 'text') {
      setUploadedFiles([])
    } else if (tab === 'files') {
      setTextInput('')
    }
  }

  const handleProcess = async () => {
    setIsProcessing(true)
    // Simulate processing
    setTimeout(() => {
      const inputSource = activeInputTab === 'text' ? 'text' : 'files'
      setResults({
        inputType: inputSource,
        textAnalysis: textInput ? `Analysis of: "${textInput.substring(0, 50)}..."` : 'No text provided',
        filesProcessed: uploadedFiles.length,
        configuration: configurations,
        timestamp: new Date().toISOString()
      })
      setIsProcessing(false)
    }, 2000)
  }

  const handleReset = () => {
    setTextInput('')
    setUploadedFiles([])
    setResults(null)
    setActiveInputTab('text')
    setConfigurations({
      analysisType: 'sentiment',
      includeMetrics: true,
      outputFormat: 'json',
      language: 'en'
    })
  }

  return (
    <main className="min-vh-100 bg-light">
      {/* Header */}
      <nav className="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div className="container">
          <span className="navbar-brand fw-bold">🤖 RoboResearcher-2000</span>
          <div className="navbar-nav ms-auto">
            <ThemeSelector currentTheme={currentTheme} onThemeChange={setCurrentTheme} />
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="bg-white py-5 border-bottom">
        <div className="container text-center">
          <h1 className="display-5 fw-bold text-primary mb-3">
            Transform Research Data into
            <br />
            <span className="text-secondary">Actionable Insights</span>
          </h1>
          <p className="lead text-muted mb-4 mx-auto" style={{maxWidth: '600px'}}>
            Seamlessly analyze research content that accelerates user discoveries and generates
            professional presentations in minutes.
          </p>

          {/* Stats Row */}
          <div className="row justify-content-center mb-5">
            <div className="col-md-2 col-4">
              <div className="text-center">
                <h3 className="text-primary fw-bold mb-1">17</h3>
                <small className="text-muted">Analysis types</small>
              </div>
            </div>
            <div className="col-md-2 col-4">
              <div className="text-center">
                <h3 className="text-primary fw-bold mb-1">15-20</h3>
                <small className="text-muted">Processing in mins</small>
              </div>
            </div>
            <div className="col-md-2 col-4">
              <div className="text-center">
                <h3 className="text-primary fw-bold mb-1">100%</h3>
                <small className="text-muted">Data secure</small>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container py-5">
        <div className="row justify-content-center">
          <div className="col-lg-10 col-xl-8">
            {/* Input Method Card */}
            <div className="card shadow-sm border-0 mb-4">
              <div className="card-header bg-white border-bottom-0 pt-4 px-4">
                <div className="text-center mb-3">
                  <h4 className="fw-semibold text-dark mb-2">📄 Upload Research Content</h4>
                  <p className="text-muted mb-0">Choose your input method to get started with analysis</p>
                </div>
                <ul className="nav nav-tabs justify-content-center border-0">
                  <li className="nav-item">
                    <button
                      className={`nav-link px-4 py-3 fw-semibold ${activeInputTab === 'text' ? 'active' : ''}`}
                      onClick={() => handleTabSwitch('text')}
                      type="button"
                    >
                      📝 Text Input
                      {textInput.trim() && <span className="badge bg-success ms-2">✓</span>}
                    </button>
                  </li>
                  <li className="nav-item">
                    <button
                      className={`nav-link px-4 py-3 fw-semibold ${activeInputTab === 'files' ? 'active' : ''}`}
                      onClick={() => handleTabSwitch('files')}
                      type="button"
                    >
                      📁 File Upload
                      {uploadedFiles.length > 0 && <span className="badge bg-success ms-2">{uploadedFiles.length}</span>}
                    </button>
                  </li>
                </ul>
              </div>

              <div className="card-body p-4">
                {activeInputTab === 'text' && (
                  <div className="p-3">
                    <TextInputSection
                      textInput={textInput}
                      onTextChange={handleTextChange}
                      disabled={uploadedFiles.length > 0}
                    />
                  </div>
                )}

                {activeInputTab === 'files' && (
                  <div className="p-3">
                    <FileUploadSection
                      uploadedFiles={uploadedFiles}
                      onFilesChange={handleFilesChange}
                      disabled={textInput.trim().length > 0}
                    />
                  </div>
                )}
              </div>

              {/* Input Status Indicator */}
              <div className="card-footer bg-light">
                <div className="d-flex justify-content-between align-items-center">
                  <small className="text-muted">
                    {activeInputTab === 'text' ? (
                      textInput.trim() ?
                        `Text ready for analysis (${textInput.length} characters)` :
                        'Enter text to analyze'
                    ) : (
                      uploadedFiles.length > 0 ?
                        `${uploadedFiles.length} file(s) ready for analysis` :
                        'Upload files to analyze'
                    )}
                  </small>
                  {(textInput.trim() || uploadedFiles.length > 0) && (
                    <span className="badge bg-success">Ready to Process</span>
                  )}
                </div>
              </div>
            </div>

            {/* Configuration and Process Section */}
            <div className="row mt-4">
              <div className="col-lg-8">
                {/* Process Button */}
                <div className="card shadow-sm border-0">
                  <div className="card-body text-center py-4">
                    <button
                      className="btn btn-primary btn-lg px-5 py-3 me-3"
                      onClick={handleProcess}
                      disabled={isProcessing || (!textInput && uploadedFiles.length === 0)}
                    >
                      {isProcessing ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                          Processing Analysis...
                        </>
                      ) : (
                        <>
                          🚀 Start Analysis
                        </>
                      )}
                    </button>
                    <button
                      className="btn btn-outline-secondary px-4 py-3"
                      onClick={handleReset}
                    >
                      🔄 Reset
                    </button>
                  </div>
                </div>
              </div>

              <div className="col-lg-4">
                <ConfigurationSection
                  configurations={configurations}
                  onConfigChange={setConfigurations}
                />
              </div>
            </div>
          </div>
        </div>

        {results && (
          <div className="row mt-5">
            <div className="col-12">
              <div className="card shadow-sm border-0">
                <div className="card-header bg-white border-bottom">
                  <h5 className="mb-0 fw-semibold">📊 Analysis Results</h5>
                </div>
                <div className="card-body p-0">
                  <ResultsSection results={results} />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </main>
  )
}

export default App
