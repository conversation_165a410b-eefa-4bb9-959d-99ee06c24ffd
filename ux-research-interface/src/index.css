/* Custom styles for UX Research Interface */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  min-height: 100vh;
}

/* Custom dropdown styles */
.dropdown-menu.show {
  display: block;
}

/* File upload area enhancements */
.uploaded-files .list-group-item {
  border-left: 4px solid var(--bs-primary);
}

/* Results section enhancements */
.nav-tabs .nav-link {
  border: none;
  color: var(--bs-secondary);
}

.nav-tabs .nav-link.active {
  background-color: var(--bs-primary);
  color: white;
  border-radius: 0.375rem 0.375rem 0 0;
}

/* Progress bar styling */
.progress {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Card hover effects */
.card {
  transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container {
    padding-left: 15px;
    padding-right: 15px;
  }

  .navbar-brand {
    font-size: 1.1rem;
  }
}

/* Theme transition */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Custom scrollbar for code blocks */
pre {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}

pre::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

pre::-webkit-scrollbar-track {
  background: transparent;
}

pre::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}
