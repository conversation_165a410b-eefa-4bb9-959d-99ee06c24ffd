function ConfigurationSection({ configurations, onConfigChange }) {
  const handleConfigChange = (key, value) => {
    onConfigChange({
      ...configurations,
      [key]: value
    })
  }

  return (
    <div className="card shadow-sm border-0">
      <div className="card-header bg-primary text-light border-bottom">
        <h6 className="mb-0 fw-semibold">⚙️ Configuration</h6>
      </div>
      <div className="card-body p-3">
        <div className="mb-2">
          <label htmlFor="analysisType" className="form-label small fw-semibold">Analysis Type</label>
          <select
            id="analysisType"
            className="form-select form-select-sm"
            value={configurations.analysisType}
            onChange={(e) => handleConfigChange('analysisType', e.target.value)}
          >
            <option value="sentiment">Sentiment Analysis</option>
            <option value="emotion">Emotion Detection</option>
            <option value="topic">Topic Modeling</option>
            <option value="keyword">Keyword Extraction</option>
            <option value="summary">Text Summarization</option>
            <option value="classification">Content Classification</option>
          </select>
        </div>

        <div className="mb-2">
          <label htmlFor="language" className="form-label small fw-semibold">Language</label>
          <select
            id="language"
            className="form-select form-select-sm"
            value={configurations.language}
            onChange={(e) => handleConfigChange('language', e.target.value)}
          >
            <option value="en">English</option>
            <option value="es">Spanish</option>
            <option value="fr">French</option>
            <option value="de">German</option>
            <option value="it">Italian</option>
            <option value="pt">Portuguese</option>
            <option value="auto">Auto-detect</option>
          </select>
        </div>

        <div className="mb-2">
          <label htmlFor="outputFormat" className="form-label small fw-semibold">Output Format</label>
          <select
            id="outputFormat"
            className="form-select form-select-sm"
            value={configurations.outputFormat}
            onChange={(e) => handleConfigChange('outputFormat', e.target.value)}
          >
            <option value="json">JSON</option>
            <option value="csv">CSV</option>
            <option value="xml">XML</option>
            <option value="txt">Plain Text</option>
          </select>
        </div>

        <div className="form-check mb-2">
          <input
            className="form-check-input"
            type="checkbox"
            id="includeMetrics"
            checked={configurations.includeMetrics}
            onChange={(e) => handleConfigChange('includeMetrics', e.target.checked)}
          />
          <label className="form-check-label small" htmlFor="includeMetrics">
            Include detailed metrics
          </label>
        </div>

        <div className="form-check mb-2">
          <input
            className="form-check-input"
            type="checkbox"
            id="includeConfidence"
            checked={configurations.includeConfidence || false}
            onChange={(e) => handleConfigChange('includeConfidence', e.target.checked)}
          />
          <label className="form-check-label small" htmlFor="includeConfidence">
            Include confidence scores
          </label>
        </div>

        <div className="form-check mb-3">
          <input
            className="form-check-input"
            type="checkbox"
            id="enablePreprocessing"
            checked={configurations.enablePreprocessing || true}
            onChange={(e) => handleConfigChange('enablePreprocessing', e.target.checked)}
          />
          <label className="form-check-label small" htmlFor="enablePreprocessing">
            Enable text preprocessing
          </label>
        </div>

        <hr className="my-3" />

        <div>
          <h6 className="small fw-semibold mb-2">Advanced Options</h6>
          <div className="mb-2">
            <label htmlFor="confidenceThreshold" className="form-label small">
              Confidence Threshold: <span className="fw-semibold">{configurations.confidenceThreshold || 0.7}</span>
            </label>
            <input
              type="range"
              className="form-range"
              id="confidenceThreshold"
              min="0.1"
              max="1.0"
              step="0.1"
              value={configurations.confidenceThreshold || 0.7}
              onChange={(e) => handleConfigChange('confidenceThreshold', parseFloat(e.target.value))}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default ConfigurationSection
