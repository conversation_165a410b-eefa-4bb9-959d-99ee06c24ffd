import { useRef } from 'react'

function FileUploadSection({ uploadedFiles, onFilesChange, disabled = false }) {
  const fileInputRef = useRef(null)

  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files)
    const newFiles = files.map(file => ({
      id: Date.now() + Math.random(),
      name: file.name,
      size: file.size,
      type: file.type,
      file: file
    }))
    onFilesChange([...uploadedFiles, ...newFiles])
  }

  const removeFile = (fileId) => {
    onFilesChange(uploadedFiles.filter(file => file.id !== fileId))
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (fileType) => {
    if (fileType.startsWith('image/')) return '🖼️'
    if (fileType.startsWith('text/')) return '📄'
    if (fileType.includes('pdf')) return '📕'
    if (fileType.includes('word')) return '📘'
    if (fileType.includes('excel') || fileType.includes('spreadsheet')) return '📊'
    return '📎'
  }

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-3">
        <h5 className="mb-0">File Upload</h5>
        <span className="badge bg-secondary">{uploadedFiles.length} files</span>
      </div>
      <div>
        <div className="mb-3">
          <input
            ref={fileInputRef}
            type="file"
            className="form-control"
            multiple
            onChange={handleFileUpload}
            accept=".txt,.pdf,.doc,.docx,.csv,.xlsx,.json,.xml"
            disabled={disabled}
          />
          <div className="form-text">
            {disabled ?
              "Switch to File Upload tab to upload files..." :
              "Supported formats: TXT, PDF, DOC, DOCX, CSV, XLSX, JSON, XML"
            }
          </div>
        </div>

        {uploadedFiles.length > 0 && (
          <div className="uploaded-files">
            <h6>Uploaded Files:</h6>
            <div className="list-group">
              {uploadedFiles.map(file => (
                <div key={file.id} className="list-group-item d-flex justify-content-between align-items-center">
                  <div className="d-flex align-items-center">
                    <span className="me-2">{getFileIcon(file.type)}</span>
                    <div>
                      <div className="fw-medium">{file.name}</div>
                      <small className="text-muted">
                        {formatFileSize(file.size)} • {file.type || 'Unknown type'}
                      </small>
                    </div>
                  </div>
                  <button
                    className="btn btn-sm btn-outline-danger"
                    onClick={() => removeFile(file.id)}
                    disabled={disabled}
                  >
                    Remove
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {uploadedFiles.length === 0 && (
          <div className="text-center py-4 text-muted">
            <div className="mb-2">📁</div>
            <p>{disabled ? "Files disabled while text input is active" : "No files uploaded yet"}</p>
            <small>
              {disabled ?
                "Clear text input to enable file uploads" :
                "Upload files to include them in your UX research analysis"
              }
            </small>
          </div>
        )}
      </div>
    </div>
  )
}

export default FileUploadSection
