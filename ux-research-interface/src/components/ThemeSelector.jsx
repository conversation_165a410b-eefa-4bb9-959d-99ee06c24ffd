import { useState, useEffect } from 'react'

const themes = [
  { name: 'default', label: 'Default' },
  { name: 'brite', label: 'Brite' },
  { name: 'cerulean', label: '<PERSON><PERSON><PERSON>' },
  { name: 'cosmo', label: 'Cosmo' },
  { name: 'cyborg', label: 'Cyborg' },
  { name: 'darkly', label: 'Darkly' },
  { name: 'flatly', label: 'Flatly' },
  { name: 'journal', label: 'Journal' },
  { name: 'litera', label: 'Litera' },
  { name: 'lumen', label: '<PERSON><PERSON>' },
  { name: 'lux', label: 'Lux' },
  { name: 'materia', label: 'Materia' },
  { name: 'minty', label: 'Minty' },
  { name: 'morph', label: 'Morph' },
  { name: 'pulse', label: 'Pulse' },
  { name: 'quartz', label: 'Quartz' },
  { name: 'sandstone', label: 'Sandstone' },
  { name: 'simplex', label: 'Simplex' },
  { name: 'sketchy', label: 'Sketchy' },
  { name: 'slate', label: 'Slate' },
  { name: 'solar', label: '<PERSON>' },
  { name: 'spacelab', label: '<PERSON>lab' },
  { name: 'superhero', label: 'Superhero' },
  { name: 'united', label: 'United' },
  { name: 'vapor', label: 'Vapor' },
  { name: 'yeti', label: 'Yeti' },
  { name: 'zephyr', label: 'Zephyr' }
]

function ThemeSelector({ currentTheme, onThemeChange }) {
  const [isOpen, setIsOpen] = useState(false)

  const changeTheme = (themeName) => {
    // Remove existing theme link
    const existingLink = document.querySelector('link[data-theme]')
    if (existingLink) {
      existingLink.remove()
    }

    // Add new theme link - using CDN for reliability
    const link = document.createElement('link')
    link.rel = 'stylesheet'

    // Use regular Bootstrap for "default" theme, Bootswatch for others
    if (themeName === 'default') {
      link.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css'
    } else {
      link.href = `https://cdn.jsdelivr.net/npm/bootswatch@5.3.0/dist/${themeName}/bootstrap.min.css`
    }

    link.setAttribute('data-theme', themeName)
    document.head.appendChild(link)

    onThemeChange(themeName)
    setIsOpen(false)
  }

  useEffect(() => {
    // Set initial theme
    changeTheme(currentTheme)
  }, [])

  return (
    <div className="dropdown">
      <button
        className="btn btn-outline-light dropdown-toggle"
        type="button"
        onClick={() => setIsOpen(!isOpen)}
      >
        Theme: {themes.find(t => t.name === currentTheme)?.label || 'Lumen'}
      </button>
      {isOpen && (
        <ul className="dropdown-menu dropdown-menu-end show">
          {themes.map(theme => (
            <li key={theme.name}>
              <button
                className={`dropdown-item ${currentTheme === theme.name ? 'active' : ''}`}
                onClick={() => changeTheme(theme.name)}
              >
                {theme.label}
              </button>
            </li>
          ))}
        </ul>
      )}
    </div>
  )
}

export default ThemeSelector
