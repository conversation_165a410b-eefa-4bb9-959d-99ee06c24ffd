import { useState, useEffect } from 'react'

const themes = [
  { name: 'lumen', label: '<PERSON><PERSON> (Light)' },
  { name: 'darkly', label: 'Darkly (Dark)' },
  { name: 'flatly', label: 'Flatly (Blue)' },
  { name: 'journal', label: 'Journal (Classic)' },
  { name: 'minty', label: '<PERSON><PERSON> (Green)' },
  { name: 'pulse', label: '<PERSON>ulse (Purple)' },
  { name: 'sandstone', label: 'Sandstone (Warm)' },
  { name: 'united', label: 'United (Orange)' }
]

function ThemeSelector({ currentTheme, onThemeChange }) {
  const [isOpen, setIsOpen] = useState(false)

  const changeTheme = (themeName) => {
    // Remove existing theme link
    const existingLink = document.querySelector('link[data-theme]')
    if (existingLink) {
      existingLink.remove()
    }

    // Add new theme link - using CDN for reliability
    const link = document.createElement('link')
    link.rel = 'stylesheet'
    link.href = `https://cdn.jsdelivr.net/npm/bootswatch@5.3.0/dist/${themeName}/bootstrap.min.css`
    link.setAttribute('data-theme', themeName)
    document.head.appendChild(link)

    onThemeChange(themeName)
    setIsOpen(false)
  }

  useEffect(() => {
    // Set initial theme
    changeTheme(currentTheme)
  }, [])

  return (
    <div className="dropdown">
      <button
        className="btn btn-outline-light dropdown-toggle"
        type="button"
        onClick={() => setIsOpen(!isOpen)}
      >
        Theme: {themes.find(t => t.name === currentTheme)?.label || 'Lumen'}
      </button>
      {isOpen && (
        <ul className="dropdown-menu dropdown-menu-end show">
          {themes.map(theme => (
            <li key={theme.name}>
              <button
                className={`dropdown-item ${currentTheme === theme.name ? 'active' : ''}`}
                onClick={() => changeTheme(theme.name)}
              >
                {theme.label}
              </button>
            </li>
          ))}
        </ul>
      )}
    </div>
  )
}

export default ThemeSelector
