import { useState } from 'react'

function TextInputSection({ textInput, onTextChange, disabled = false }) {
  const [wordCount, setWordCount] = useState(0)
  const [charCount, setCharCount] = useState(0)

  const handleTextChange = (e) => {
    const text = e.target.value
    onTextChange(text)
    
    // Update counts
    setCharCount(text.length)
    setWordCount(text.trim() === '' ? 0 : text.trim().split(/\s+/).length)
  }

  const handleClear = () => {
    onTextChange('')
    setWordCount(0)
    setCharCount(0)
  }

  const handleSampleText = () => {
    const sampleText = `This is a sample text for UX research analysis. Users often provide feedback about their experience with our product. They mention both positive aspects like ease of use and areas for improvement such as loading times. Understanding user sentiment helps us make better design decisions and improve overall user satisfaction.`
    onTextChange(sampleText)
    setCharCount(sampleText.length)
    setWordCount(sampleText.trim().split(/\s+/).length)
  }

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-3">
        <h5 className="mb-0">Text Input</h5>
        <div>
          <button
            className="btn btn-sm btn-outline-primary me-2"
            onClick={handleSampleText}
            disabled={disabled}
          >
            Load Sample
          </button>
          <button
            className="btn btn-sm btn-outline-secondary"
            onClick={handleClear}
            disabled={!textInput || disabled}
          >
            Clear
          </button>
        </div>
      </div>
      <div>
        <div className="mb-3">
          <label htmlFor="textInput" className="form-label">
            Enter text for analysis:
          </label>
          <textarea
            id="textInput"
            className={`form-control ${disabled ? 'bg-light' : ''}`}
            rows="8"
            value={textInput}
            onChange={handleTextChange}
            placeholder={disabled ? "Switch to Text Input tab to enter text..." : "Paste or type your text here for UX research analysis..."}
            disabled={disabled}
          />
        </div>
        <div className="d-flex justify-content-between text-muted small">
          <span>Characters: {charCount}</span>
          <span>Words: {wordCount}</span>
        </div>
        {textInput && !disabled && (
          <div className="mt-2">
            <div className="progress" style={{ height: '4px' }}>
              <div
                className="progress-bar"
                role="progressbar"
                style={{ width: `${Math.min((charCount / 1000) * 100, 100)}%` }}
              ></div>
            </div>
            <small className="text-muted">
              {charCount < 1000 ?
                `${1000 - charCount} characters remaining for optimal analysis` :
                'Text length is optimal for analysis'
              }
            </small>
          </div>
        )}
      </div>
    </div>
  )
}

export default TextInputSection
