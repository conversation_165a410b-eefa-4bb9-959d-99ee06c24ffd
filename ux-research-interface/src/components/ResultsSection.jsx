import { useState } from 'react'

function ResultsSection({ results }) {
  const [activeTab, setActiveTab] = useState('overview')

  const downloadResults = () => {
    const dataStr = JSON.stringify(results, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = `ux-research-results-${new Date().toISOString().split('T')[0]}.json`
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(JSON.stringify(results, null, 2))
      .then(() => alert('Results copied to clipboard!'))
      .catch(() => alert('Failed to copy results'))
  }

  return (
    <div className="card">
      <div className="card-header d-flex justify-content-between align-items-center">
        <h5 className="mb-0">Analysis Results</h5>
        <div>
          <button 
            className="btn btn-sm btn-outline-primary me-2" 
            onClick={copyToClipboard}
          >
            Copy
          </button>
          <button 
            className="btn btn-sm btn-primary" 
            onClick={downloadResults}
          >
            Download
          </button>
        </div>
      </div>
      <div className="card-body">
        <ul className="nav nav-tabs mb-3">
          <li className="nav-item">
            <button 
              className={`nav-link ${activeTab === 'overview' ? 'active' : ''}`}
              onClick={() => setActiveTab('overview')}
            >
              Overview
            </button>
          </li>
          <li className="nav-item">
            <button 
              className={`nav-link ${activeTab === 'details' ? 'active' : ''}`}
              onClick={() => setActiveTab('details')}
            >
              Details
            </button>
          </li>
          <li className="nav-item">
            <button 
              className={`nav-link ${activeTab === 'raw' ? 'active' : ''}`}
              onClick={() => setActiveTab('raw')}
            >
              Raw Data
            </button>
          </li>
        </ul>

        {activeTab === 'overview' && (
          <div>
            <div className="row">
              <div className="col-md-6">
                <div className="card bg-light">
                  <div className="card-body">
                    <h6 className="card-title">Processing Summary</h6>
                    <p className="card-text">
                      <strong>Files Processed:</strong> {results.filesProcessed}<br/>
                      <strong>Analysis Type:</strong> {results.configuration.analysisType}<br/>
                      <strong>Language:</strong> {results.configuration.language}<br/>
                      <strong>Timestamp:</strong> {new Date(results.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
              <div className="col-md-6">
                <div className="card bg-light">
                  <div className="card-body">
                    <h6 className="card-title">Text Analysis</h6>
                    <p className="card-text">
                      {results.textAnalysis}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'details' && (
          <div>
            <h6>Configuration Used</h6>
            <div className="table-responsive">
              <table className="table table-sm">
                <tbody>
                  {Object.entries(results.configuration).map(([key, value]) => (
                    <tr key={key}>
                      <td><strong>{key.charAt(0).toUpperCase() + key.slice(1)}:</strong></td>
                      <td>{typeof value === 'boolean' ? (value ? 'Yes' : 'No') : value.toString()}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            <h6 className="mt-4">Processing Metrics</h6>
            <div className="alert alert-info">
              <strong>Note:</strong> This is a demo interface. In a real implementation, 
              this section would contain detailed analysis metrics, confidence scores, 
              sentiment breakdowns, topic distributions, and other relevant UX research insights.
            </div>
          </div>
        )}

        {activeTab === 'raw' && (
          <div>
            <pre className="bg-light p-3 rounded" style={{ fontSize: '0.875rem', maxHeight: '400px', overflow: 'auto' }}>
              {JSON.stringify(results, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}

export default ResultsSection
