import { useState } from 'react'
import './App.css'
import ThemeSelector from './components/ThemeSelector'
import TextInputSection from './components/TextInputSection'
import FileUploadSection from './components/FileUploadSection'
import ConfigurationSection from './components/ConfigurationSection'
import ResultsSection from './components/ResultsSection'

function App() {
  const [currentTheme, setCurrentTheme] = useState('lumen')
  const [textInput, setTextInput] = useState('')
  const [uploadedFiles, setUploadedFiles] = useState([])
  const [activeInputTab, setActiveInputTab] = useState('text') // 'text' or 'files'
  const [configurations, setConfigurations] = useState({
    analysisType: 'sentiment',
    includeMetrics: true,
    outputFormat: 'json',
    language: 'en'
  })
  const [results, setResults] = useState(null)
  const [isProcessing, setIsProcessing] = useState(false)

  // Handle mutual exclusivity between text and file inputs
  const handleTextChange = (text) => {
    setTextInput(text)
    if (text.trim() && uploadedFiles.length > 0) {
      setUploadedFiles([]) // Clear files when text is entered
    }
    if (text.trim() && activeInputTab !== 'text') {
      setActiveInputTab('text')
    }
  }

  const handleFilesChange = (files) => {
    setUploadedFiles(files)
    if (files.length > 0 && textInput.trim()) {
      setTextInput('') // Clear text when files are uploaded
    }
    if (files.length > 0 && activeInputTab !== 'files') {
      setActiveInputTab('files')
    }
  }

  const handleTabSwitch = (tab) => {
    setActiveInputTab(tab)
    // Clear the other input when switching tabs
    if (tab === 'text') {
      setUploadedFiles([])
    } else if (tab === 'files') {
      setTextInput('')
    }
  }

  const handleProcess = async () => {
    setIsProcessing(true)
    // Simulate processing
    setTimeout(() => {
      const inputSource = activeInputTab === 'text' ? 'text' : 'files'
      setResults({
        inputType: inputSource,
        textAnalysis: textInput ? `Analysis of: "${textInput.substring(0, 50)}..."` : 'No text provided',
        filesProcessed: uploadedFiles.length,
        configuration: configurations,
        timestamp: new Date().toISOString()
      })
      setIsProcessing(false)
    }, 2000)
  }

  const handleReset = () => {
    setTextInput('')
    setUploadedFiles([])
    setResults(null)
    setActiveInputTab('text')
    setConfigurations({
      analysisType: 'sentiment',
      includeMetrics: true,
      outputFormat: 'json',
      language: 'en'
    })
  }

  return (
    <main>
      <nav className="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
        <div className="container">
          <span className="navbar-brand">RoboResearcher-2000</span>
          <div className="navbar-nav ms-auto">
            <ThemeSelector currentTheme={currentTheme} onThemeChange={setCurrentTheme} />
          </div>
        </div>
      </nav>

      <div className="container">
        <div className="row">
          <div className="col-lg-8">
            {/* Input Method Tabs */}

            
            
              
                <ul className="nav nav-tabs ">
                  <li className="nav-item">
                    <button
                      className={`nav-link ${activeInputTab === 'text' ? 'active' : ''}`}
                      onClick={() => handleTabSwitch('text')}
                      type="button"
                    >
                      📝 Text Input
                      {textInput.trim() && <span className="badge bg-primary ms-2">✓</span>}
                    </button>
                  </li>
                  <li className="nav-item">
                    <button
                      className={`nav-link ${activeInputTab === 'files' ? 'active' : ''}`}
                      onClick={() => handleTabSwitch('files')}
                      type="button"
                    >
                      📁 File Upload
                      {uploadedFiles.length > 0 && <span className="badge bg-primary ms-2">{uploadedFiles.length}</span>}
                    </button>
                  </li>
                </ul>
              

              <div className="card-body p-0">
                {activeInputTab === 'text' && (
                  <div className="p-3">
                    <TextInputSection
                      textInput={textInput}
                      onTextChange={handleTextChange}
                      disabled={uploadedFiles.length > 0}
                    />
                  </div>
                )}

                {activeInputTab === 'files' && (
                  <div className="p-3">
                    <FileUploadSection
                      uploadedFiles={uploadedFiles}
                      onFilesChange={handleFilesChange}
                      disabled={textInput.trim().length > 0}
                    />
                  </div>
                )}
              </div>

              {/* Input Status Indicator */}
              <div className="card-footer bg-light">
                <div className="d-flex justify-content-between align-items-center">
                  <small className="text-muted">
                    {activeInputTab === 'text' ? (
                      textInput.trim() ?
                        `Text ready for analysis (${textInput.length} characters)` :
                        'Enter text to analyze'
                    ) : (
                      uploadedFiles.length > 0 ?
                        `${uploadedFiles.length} file(s) ready for analysis` :
                        'Upload files to analyze'
                    )}
                  </small>
                  {(textInput.trim() || uploadedFiles.length > 0) && (
                    <span className="badge bg-success">Ready to Process</span>
                  )}
                </div>
              </div>
            
          </div>

          <div className="col-lg-4">
            <ConfigurationSection
              configurations={configurations}
              onConfigChange={setConfigurations}
            />

            <div className="card mt-3">
              <div className="card-body text-center">
                <button
                  className="btn btn-success btn-lg me-2"
                  onClick={handleProcess}
                  disabled={isProcessing || (!textInput && uploadedFiles.length === 0)}
                >
                  {isProcessing ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                      Processing...
                    </>
                  ) : (
                    'Process Data'
                  )}
                </button>
                <button
                  className="btn btn-outline-secondary"
                  onClick={handleReset}
                >
                  Reset
                </button>
              </div>
            </div>
          </div>
        </div>

        {results && (
          <div className="row mt-4">
            <div className="col-12">
              <ResultsSection results={results} />
            </div>
          </div>
        )}
      </div>
    </main>
  )
}

export default App
