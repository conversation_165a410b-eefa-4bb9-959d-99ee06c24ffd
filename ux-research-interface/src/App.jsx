import { useState } from 'react'
import './App.css'
import ThemeSelector from './components/ThemeSelector'
import TextInputSection from './components/TextInputSection'
import FileUploadSection from './components/FileUploadSection'
import ConfigurationSection from './components/ConfigurationSection'
import ResultsSection from './components/ResultsSection'

function App() {
  const [currentTheme, setCurrentTheme] = useState('lumen')
  const [textInput, setTextInput] = useState('')
  const [uploadedFiles, setUploadedFiles] = useState([])
  const [configurations, setConfigurations] = useState({
    analysisType: 'sentiment',
    includeMetrics: true,
    outputFormat: 'json',
    language: 'en'
  })
  const [results, setResults] = useState(null)
  const [isProcessing, setIsProcessing] = useState(false)

  const handleProcess = async () => {
    setIsProcessing(true)
    // Simulate processing
    setTimeout(() => {
      setResults({
        textAnalysis: textInput ? `Analysis of: "${textInput.substring(0, 50)}..."` : 'No text provided',
        filesProcessed: uploadedFiles.length,
        configuration: configurations,
        timestamp: new Date().toISOString()
      })
      setIsProcessing(false)
    }, 2000)
  }

  const handleReset = () => {
    setTextInput('')
    setUploadedFiles([])
    setResults(null)
    setConfigurations({
      analysisType: 'sentiment',
      includeMetrics: true,
      outputFormat: 'json',
      language: 'en'
    })
  }

  return (
    <main>
      <nav className="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
        <div className="container">
          <span className="navbar-brand">RoboResearcher-2000</span>
          <div className="navbar-nav ms-auto">
            <ThemeSelector currentTheme={currentTheme} onThemeChange={setCurrentTheme} />
          </div>
        </div>
      </nav>

      <div className="container">
        <div className="row">
          <div className="col-lg-8">
            <TextInputSection
              textInput={textInput}
              onTextChange={setTextInput}
            />

            <FileUploadSection
              uploadedFiles={uploadedFiles}
              onFilesChange={setUploadedFiles}
            />
          </div>

          <div className="col-lg-4">
            <ConfigurationSection
              configurations={configurations}
              onConfigChange={setConfigurations}
            />

            <div className="card mt-3">
              <div className="card-body text-center">
                <button
                  className="btn btn-success btn-lg me-2"
                  onClick={handleProcess}
                  disabled={isProcessing || (!textInput && uploadedFiles.length === 0)}
                >
                  {isProcessing ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                      Processing...
                    </>
                  ) : (
                    'Process Data'
                  )}
                </button>
                <button
                  className="btn btn-outline-secondary"
                  onClick={handleReset}
                >
                  Reset
                </button>
              </div>
            </div>
          </div>
        </div>

        {results && (
          <div className="row mt-4">
            <div className="col-12">
              <ResultsSection results={results} />
            </div>
          </div>
        )}
      </div>
    </main>
  )
}

export default App
